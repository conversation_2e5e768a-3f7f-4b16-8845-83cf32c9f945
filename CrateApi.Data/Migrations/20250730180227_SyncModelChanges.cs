using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class SyncModelChanges : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_collection_content_mapping_collections_collection_id",
                schema: "crate",
                table: "collection_content_mapping");

            migrationBuilder.DropForeignKey(
                name: "fk_collection_content_mapping_content_content_id",
                schema: "crate",
                table: "collection_content_mapping");

            migrationBuilder.DropPrimaryKey(
                name: "pk_collection_content_mapping",
                schema: "crate",
                table: "collection_content_mapping");

            migrationBuilder.RenameTable(
                name: "collection_content_mapping",
                schema: "crate",
                newName: "collection_content_mappings",
                newSchema: "crate");

            migrationBuilder.RenameIndex(
                name: "ix_collection_content_mapping_content_id",
                schema: "crate",
                table: "collection_content_mappings",
                newName: "ix_collection_content_mappings_content_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_collection_content_mappings",
                schema: "crate",
                table: "collection_content_mappings",
                columns: new[] { "collection_id", "content_id" });

            migrationBuilder.AddForeignKey(
                name: "fk_collection_content_mappings_collections_collection_id",
                schema: "crate",
                table: "collection_content_mappings",
                column: "collection_id",
                principalSchema: "crate",
                principalTable: "collections",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_collection_content_mappings_content_content_id",
                schema: "crate",
                table: "collection_content_mappings",
                column: "content_id",
                principalSchema: "crate",
                principalTable: "content",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_collection_content_mappings_collections_collection_id",
                schema: "crate",
                table: "collection_content_mappings");

            migrationBuilder.DropForeignKey(
                name: "fk_collection_content_mappings_content_content_id",
                schema: "crate",
                table: "collection_content_mappings");

            migrationBuilder.DropPrimaryKey(
                name: "pk_collection_content_mappings",
                schema: "crate",
                table: "collection_content_mappings");

            migrationBuilder.RenameTable(
                name: "collection_content_mappings",
                schema: "crate",
                newName: "collection_content_mapping",
                newSchema: "crate");

            migrationBuilder.RenameIndex(
                name: "ix_collection_content_mappings_content_id",
                schema: "crate",
                table: "collection_content_mapping",
                newName: "ix_collection_content_mapping_content_id");

            migrationBuilder.AddPrimaryKey(
                name: "pk_collection_content_mapping",
                schema: "crate",
                table: "collection_content_mapping",
                columns: new[] { "collection_id", "content_id" });

            migrationBuilder.AddForeignKey(
                name: "fk_collection_content_mapping_collections_collection_id",
                schema: "crate",
                table: "collection_content_mapping",
                column: "collection_id",
                principalSchema: "crate",
                principalTable: "collections",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_collection_content_mapping_content_content_id",
                schema: "crate",
                table: "collection_content_mapping",
                column: "content_id",
                principalSchema: "crate",
                principalTable: "content",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
